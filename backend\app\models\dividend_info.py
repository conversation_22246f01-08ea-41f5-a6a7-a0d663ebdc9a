from datetime import date, datetime
from decimal import Decimal

from app.db.base_class import Base
from sqlalchemy import Column, Date, DateTime, ForeignKey, Integer, Numeric
from sqlalchemy.orm import relationship


class DividendInfo(Base):
    __tablename__ = 'dividend_info'
    __table_args__ = {'schema': 'dse_schema'}

    id = Column(Integer, primary_key=True, index=True)
    portfolio_id = Column(Integer, ForeignKey('dse_schema.portfolios.id'), nullable=False)
    trading_code_id = Column(Integer, ForeignKey('dse_schema.trading_codes.id'), nullable=False)
    last_record_date = Column(Date, nullable=True)
    next_record_date = Column(Date, nullable=True)
    dividend_per_share = Column(Numeric(10, 2), default=Decimal('0.00'))
    created_at = Column(DateTime(timezone=True), default=datetime.now)
    updated_at = Column(DateTime(timezone=True), default=datetime.now, onupdate=datetime.now)

    # Relationships
    portfolio = relationship("Portfolio", back_populates="dividend_infos")
    trading_code = relationship("TradingCode")
