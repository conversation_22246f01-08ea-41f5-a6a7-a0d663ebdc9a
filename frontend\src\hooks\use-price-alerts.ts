"use client";

import { showNotification } from "@/lib/notificationService";
import { priceAlertDB } from "@/lib/priceAlertService";
import { useCallback, useRef, useState } from "react";

interface LivePrice {
  trading_code: string;
  price: number;
  last_updated: string;
}

interface UsePriceAlertsReturn {
  checkPriceAlerts: (livePrices: Record<string, LivePrice>) => Promise<void>;
  isCheckingAlerts: boolean;
}

export function usePriceAlerts(): UsePriceAlertsReturn {
  const [isCheckingAlerts, setIsCheckingAlerts] = useState(false);
  const lastNotificationTimeRef = useRef<Record<string, number>>({});
  const NOTIFICATION_COOLDOWN_MS = 5 * 60 * 1000; // 5 minutes cooldown between notifications for same alert

  const checkPriceAlerts = useCallback(
    async (livePrices: Record<string, LivePrice>) => {
      if (Object.keys(livePrices).length === 0) return;

      setIsCheckingAlerts(true);
      try {
        // Get all price alerts from IndexedDB
        const allAlerts = await priceAlertDB.getAllPriceAlerts();

        if (allAlerts.length === 0) {
          setIsCheckingAlerts(false);
          return;
        }

        const now = Date.now();

        for (const alert of allAlerts) {
          const livePrice = livePrices[alert.tradingCode];

          if (!livePrice) continue; // No live price data for this trading code

          const currentPrice = livePrice.price;
          const alertKey = alert.tradingCode;
          const lastNotificationTime =
            lastNotificationTimeRef.current[alertKey] || 0;

          // Check cooldown period
          if (now - lastNotificationTime < NOTIFICATION_COOLDOWN_MS) {
            continue;
          }

          let shouldNotify = false;
          let notificationTitle = "";
          let notificationBody = "";

          // Check buy under condition
          if (alert.buyUnder !== null && currentPrice <= alert.buyUnder) {
            shouldNotify = true;
            notificationTitle = `Buy Alert: ${alert.tradingCode}`;
            notificationBody = `Price dropped to ৳${currentPrice.toFixed(
              2
            )} (Target: ৳${alert.buyUnder.toFixed(2)})`;
          }
          // Check sell over condition
          else if (alert.sellOver !== null && currentPrice >= alert.sellOver) {
            shouldNotify = true;
            notificationTitle = `Sell Alert: ${alert.tradingCode}`;
            notificationBody = `Price rose to ৳${currentPrice.toFixed(
              2
            )} (Target: ৳${alert.sellOver.toFixed(2)})`;
          }

          if (shouldNotify) {
            // Show browser notification
            showNotification(notificationTitle, {
              body: notificationBody,
              icon: "/favicon.ico",
              tag: `price-alert-${alert.tradingCode}`, // Prevents duplicate notifications
              requireInteraction: true, // Keep notification visible until user interacts
            });

            // Update last notification time
            lastNotificationTimeRef.current[alertKey] = now;

            console.log(`Price alert triggered for ${alert.tradingCode}:`, {
              currentPrice,
              buyUnder: alert.buyUnder,
              sellOver: alert.sellOver,
            });
          }
        }
      } catch (error) {
        console.error("Error checking price alerts:", error);
      } finally {
        setIsCheckingAlerts(false);
      }
    },
    []
  );

  return {
    checkPriceAlerts,
    isCheckingAlerts,
  };
}
