from datetime import date, datetime
from decimal import Decimal

from app.db.base_class import Base
from sqlalchemy import Column, Date, DateTime, ForeignKey, Integer, Numeric
from sqlalchemy.orm import relationship


class ReceivedDividend(Base):
    __tablename__ = 'received_dividends'
    __table_args__ = {'schema': 'dse_schema'}

    id = Column(Integer, primary_key=True, index=True)
    portfolio_id = Column(Integer, ForeignKey('dse_schema.portfolios.id'), nullable=False)
    trading_code_id = Column(Integer, ForeignKey('dse_schema.trading_codes.id'), nullable=False)
    record_date = Column(Date, nullable=False)
    share_count = Column(Integer, nullable=False)
    dividend_per_share = Column(Numeric(10, 2), nullable=False)
    gross_dividend = Column(Numeric(10, 2), nullable=False)  # Total dividend before tax
    tax_amount = Column(Numeric(10, 2), nullable=False)  # 10% tax
    net_dividend = Column(Numeric(10, 2), nullable=False)  # Final amount after tax
    created_at = Column(DateTime(timezone=True), default=datetime.now)

    # Relationships
    portfolio = relationship("Portfolio", back_populates="received_dividends")
    trading_code = relationship("TradingCode")
