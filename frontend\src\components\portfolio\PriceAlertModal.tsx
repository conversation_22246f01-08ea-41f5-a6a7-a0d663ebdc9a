"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useToast } from "@/hooks/use-toast";
import { priceAlertDB, type PriceAlert } from "@/lib/priceAlertService";
import { useCallback, useEffect, useState } from "react";

interface PriceAlertModalProps {
  isOpen: boolean;
  onClose: () => void;
  tradingCode: string;
  currentPrice?: number;
}

export default function PriceAlertModal({
  isOpen,
  onClose,
  tradingCode,
  currentPrice,
}: PriceAlertModalProps) {
  const { toast } = useToast();
  const [buyUnder, setBuyUnder] = useState<string>("");
  const [sellOver, setSellOver] = useState<string>("");
  const [isLoading, setIsLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Load existing alert when modal opens
  const loadExistingAlert = useCallback(async () => {
    if (!isOpen || !tradingCode) return;

    setIsLoading(true);
    try {
      const existingAlert = await priceAlertDB.getPriceAlert(tradingCode);
      if (existingAlert) {
        setBuyUnder(existingAlert.buyUnder?.toString() || "");
        setSellOver(existingAlert.sellOver?.toString() || "");
      } else {
        setBuyUnder("");
        setSellOver("");
      }
    } catch (error) {
      console.error("Error loading price alert:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to load existing price alert.",
      });
    } finally {
      setIsLoading(false);
    }
  }, [isOpen, tradingCode, toast]);

  useEffect(() => {
    loadExistingAlert();
  }, [loadExistingAlert]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!tradingCode) return;

    setIsSubmitting(true);
    try {
      const buyUnderValue = buyUnder ? parseFloat(buyUnder) : null;
      const sellOverValue = sellOver ? parseFloat(sellOver) : null;

      // Validation
      if (buyUnderValue !== null && buyUnderValue <= 0) {
        toast({
          variant: "destructive",
          title: "Invalid Input",
          description: "Buy Under price must be greater than 0.",
        });
        return;
      }

      if (sellOverValue !== null && sellOverValue <= 0) {
        toast({
          variant: "destructive",
          title: "Invalid Input",
          description: "Sell Over price must be greater than 0.",
        });
        return;
      }

      if (
        buyUnderValue !== null &&
        sellOverValue !== null &&
        buyUnderValue >= sellOverValue
      ) {
        toast({
          variant: "destructive",
          title: "Invalid Input",
          description: "Buy Under price must be less than Sell Over price.",
        });
        return;
      }

      // If both values are null/empty, delete the alert
      if (buyUnderValue === null && sellOverValue === null) {
        await priceAlertDB.deletePriceAlert(tradingCode);
        toast({
          title: "Price Alert Removed",
          description: `Price alert for ${tradingCode} has been removed.`,
        });
      } else {
        // Save the alert
        const alert: PriceAlert = {
          tradingCode,
          buyUnder: buyUnderValue,
          sellOver: sellOverValue,
          lastUpdated: new Date().toISOString(),
        };

        await priceAlertDB.setPriceAlert(alert);
        toast({
          title: "Price Alert Set",
          description: `Price alert for ${tradingCode} has been saved.`,
        });
      }

      onClose();
    } catch (error) {
      console.error("Error saving price alert:", error);
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to save price alert.",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    setBuyUnder("");
    setSellOver("");
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>
            Price Alert for {tradingCode}
            {currentPrice && (
              <span className="text-sm font-normal text-muted-foreground ml-2">
                (Current: ৳{currentPrice.toFixed(2)})
              </span>
            )}
          </DialogTitle>
        </DialogHeader>

        {isLoading ? (
          <div className="flex justify-center py-8">
            <div className="w-6 h-6 border-2 border-t-blue-500 border-b-blue-500 rounded-full animate-spin"></div>
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="buyUnder">Buy when Under BDT</Label>
              <Input
                id="buyUnder"
                type="number"
                step="0.01"
                min="0"
                value={buyUnder}
                onChange={(e) => setBuyUnder(e.target.value)}
                placeholder="Enter price threshold for buying"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="sellOver">Sell when over BDT</Label>
              <Input
                id="sellOver"
                type="number"
                step="0.01"
                min="0"
                value={sellOver}
                onChange={(e) => setSellOver(e.target.value)}
                placeholder="Enter price threshold for selling"
              />
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? "Saving..." : "Save Alert"}
              </Button>
            </DialogFooter>
          </form>
        )}
      </DialogContent>
    </Dialog>
  );
}
