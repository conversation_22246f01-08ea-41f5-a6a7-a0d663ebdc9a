from app.db.base_class import Base
from sqlalchemy import Column, Float, Foreign<PERSON>ey, Integer
from sqlalchemy.orm import relationship


class Portfolio(Base):
    __tablename__ = 'portfolios'
    __table_args__ = {'schema': 'dse_schema'}

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey('dse_schema.users.id'), nullable=False)
    commission = Column(Float, default=0.0)
    investment_amount = Column(Float, default=0.0)
    realized_gain = Column(Float, default=0.0)

    # Relationships
    entries = relationship("PortfolioEntry", back_populates="portfolio", cascade="all, delete-orphan", lazy="selectin")
    dividend_infos = relationship("DividendInfo", back_populates="portfolio", cascade="all, delete-orphan", lazy="selectin")
    received_dividends = relationship("ReceivedDividend", back_populates="portfolio", cascade="all, delete-orphan", lazy="selectin")
    # The user relationship is defined in the User model with a backref
