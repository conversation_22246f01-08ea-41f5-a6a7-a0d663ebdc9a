"use client";

import { Input } from "@/components/ui/input";
import { useSession } from "next-auth/react";
import { useCallback, useState } from "react";

interface EditableDividendCellProps {
  value: string | number | null | undefined;
  tradingCodeId: number;
  field: "last_record_date" | "next_record_date" | "dividend_per_share";
  type: "date" | "number";
  onUpdate?: () => void;
  placeholder?: string;
}

export default function EditableDividendCell({
  value,
  tradingCodeId,
  field,
  type,
  onUpdate,
  placeholder = "—",
}: EditableDividendCellProps) {
  const { data: session } = useSession();
  const [isEditing, setIsEditing] = useState(false);
  const [editValue, setEditValue] = useState(value?.toString() || "");
  const [isUpdating, setIsUpdating] = useState(false);

  const handleClick = useCallback(() => {
    setIsEditing(true);
    setEditValue(value?.toString() || "");
  }, [value]);

  const handleBlur = useCallback(async () => {
    setIsEditing(false);

    if (!session?.user?.accessToken) return;

    // Only update if value changed
    const currentValue = value?.toString() || "";
    if (editValue === currentValue) return;

    setIsUpdating(true);
    try {
      const updateData: any = {};

      if (type === "date") {
        updateData[field] = editValue || null;
      } else if (type === "number") {
        updateData[field] = editValue ? parseFloat(editValue) : 0;
      }

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_URL}/users/portfolio/dividend-info/${tradingCodeId}`,
        {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${session.user.accessToken}`,
          },
          body: JSON.stringify(updateData),
        }
      );

      if (response.ok) {
        onUpdate?.();
      } else {
        console.error("Failed to update dividend info");
        // Reset to original value on error
        setEditValue(value?.toString() || "");
      }
    } catch (error) {
      console.error("Error updating dividend info:", error);
      // Reset to original value on error
      setEditValue(value?.toString() || "");
    } finally {
      setIsUpdating(false);
    }
  }, [editValue, value, field, type, tradingCodeId, session, onUpdate]);

  const handleKeyDown = useCallback(
    (e: React.KeyboardEvent) => {
      if (e.key === "Enter") {
        handleBlur();
      } else if (e.key === "Escape") {
        setIsEditing(false);
        setEditValue(value?.toString() || "");
      }
    },
    [handleBlur, value]
  );

  const displayValue = () => {
    if (type === "date" && value) {
      return new Date(value.toString()).toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
      });
    } else if (type === "number" && value) {
      return `BDT ${parseFloat(value.toString()).toFixed(2)}`;
    }
    return placeholder;
  };

  if (isEditing) {
    return (
      <Input
        type={type}
        value={editValue}
        onChange={(e) => setEditValue(e.target.value)}
        onBlur={handleBlur}
        onKeyDown={handleKeyDown}
        className="h-8 w-full text-sm"
        autoFocus
        step={type === "number" ? "0.01" : undefined}
        min={type === "number" ? "0" : undefined}
      />
    );
  }

  return (
    <div
      className={`cursor-pointer text-gray-500 hover:text-gray-700 ${
        isUpdating ? "opacity-50" : ""
      }`}
      onClick={handleClick}
      title="Click to edit"
    >
      {displayValue()}
    </div>
  );
}
