-- Migration to add dividend tracking tables
-- Run this migration to add dividend functionality

-- Create dividend_info table for storing dividend information per trading code per user
CREATE TABLE IF NOT EXISTS dse_schema.dividend_info (
    id SERIAL PRIMARY KEY,
    portfolio_id INTEGER NOT NULL REFERENCES dse_schema.portfolios(id) ON DELETE CASCADE,
    trading_code_id INTEGER NOT NULL REFERENCES dse_schema.trading_codes(id) ON DELETE CASCADE,
    last_record_date DATE NULL,
    next_record_date DATE NULL,
    dividend_per_share DECIMAL(10,2) DEFAULT 0.00,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(portfolio_id, trading_code_id)
);

-- Create received_dividends table for tracking historical dividend receipts
CREATE TABLE IF NOT EXISTS dse_schema.received_dividends (
    id SERIAL PRIMARY KEY,
    portfolio_id INTEGER NOT NULL REFERENCES dse_schema.portfolios(id) ON DELETE CASCADE,
    trading_code_id INTEGER NOT NULL REFERENCES dse_schema.trading_codes(id) ON DELETE CASCADE,
    record_date DATE NOT NULL,
    share_count INTEGER NOT NULL,
    dividend_per_share DECIMAL(10,2) NOT NULL,
    gross_dividend DECIMAL(10,2) NOT NULL, -- Total dividend before tax
    tax_amount DECIMAL(10,2) NOT NULL, -- 10% tax
    net_dividend DECIMAL(10,2) NOT NULL, -- Final amount after tax
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_dividend_info_portfolio_id ON dse_schema.dividend_info(portfolio_id);
CREATE INDEX IF NOT EXISTS idx_dividend_info_trading_code_id ON dse_schema.dividend_info(trading_code_id);
CREATE INDEX IF NOT EXISTS idx_dividend_info_next_record_date ON dse_schema.dividend_info(next_record_date);
CREATE INDEX IF NOT EXISTS idx_received_dividends_portfolio_id ON dse_schema.received_dividends(portfolio_id);
CREATE INDEX IF NOT EXISTS idx_received_dividends_trading_code_id ON dse_schema.received_dividends(trading_code_id);
CREATE INDEX IF NOT EXISTS idx_received_dividends_record_date ON dse_schema.received_dividends(record_date);

-- Add comments to explain the tables
COMMENT ON TABLE dse_schema.dividend_info IS 'Stores dividend information for each trading code in user portfolios';
COMMENT ON COLUMN dse_schema.dividend_info.last_record_date IS 'Last record date for dividend eligibility';
COMMENT ON COLUMN dse_schema.dividend_info.next_record_date IS 'Next upcoming record date for dividend eligibility';
COMMENT ON COLUMN dse_schema.dividend_info.dividend_per_share IS 'Dividend amount per share in BDT';

COMMENT ON TABLE dse_schema.received_dividends IS 'Historical record of all dividends received by users';
COMMENT ON COLUMN dse_schema.received_dividends.record_date IS 'The record date when dividend was earned';
COMMENT ON COLUMN dse_schema.received_dividends.share_count IS 'Number of shares held on record date';
COMMENT ON COLUMN dse_schema.received_dividends.gross_dividend IS 'Total dividend before tax deduction';
COMMENT ON COLUMN dse_schema.received_dividends.tax_amount IS 'Tax amount deducted (10%)';
COMMENT ON COLUMN dse_schema.received_dividends.net_dividend IS 'Final dividend amount after tax';
